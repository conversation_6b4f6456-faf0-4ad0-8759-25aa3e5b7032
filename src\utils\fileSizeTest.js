/**
 * 文件大小格式化功能测试
 * 用于验证格式化结果是否符合预期
 */

/**
 * 格式化文件大小（与主组件中的方法相同）
 */
function formatFileSize(bytes) {
  // 处理无效输入
  if (!bytes || bytes === 0 || bytes === '0') return '0 B';
  
  // 转换为数字
  const numBytes = Number(bytes);
  if (isNaN(numBytes) || numBytes < 0) return '未知大小';
  
  // 定义单位数组
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  
  // 计算单位级别
  const i = Math.floor(Math.log(numBytes) / Math.log(k));
  
  // 确保不超出单位数组范围
  const unitIndex = Math.min(i, units.length - 1);
  
  // 计算转换后的数值
  const size = numBytes / Math.pow(k, unitIndex);
  
  // 格式化数值
  let formattedSize;
  if (unitIndex === 0) {
    // 字节不显示小数
    formattedSize = size.toString();
  } else if (size >= 100) {
    // 大于等于100时不显示小数
    formattedSize = Math.round(size).toString();
  } else if (size >= 10) {
    // 10-99之间显示1位小数
    formattedSize = size.toFixed(1);
  } else {
    // 小于10显示2位小数
    formattedSize = size.toFixed(2);
  }
  
  // 移除末尾的0和小数点
  formattedSize = formattedSize.replace(/\.?0+$/, '');
  
  return `${formattedSize} ${units[unitIndex]}`;
}

// 测试用例
const testCases = [
  // 您提供的示例数据
  { input: '114661', expected: '112 KB', description: '示例数据1' },
  { input: '256212', expected: '250 KB', description: '示例数据2' },
  { input: '67431', expected: '65.9 KB', description: '示例数据3' },
  { input: '28386', expected: '27.7 KB', description: '示例数据4' },
  
  // 边界情况
  { input: 0, expected: '0 B', description: '零字节' },
  { input: 512, expected: '512 B', description: '字节级别' },
  { input: 1024, expected: '1 KB', description: '1KB整数' },
  { input: 1536, expected: '1.5 KB', description: '1.5KB小数' },
  { input: 10240, expected: '10 KB', description: '10KB' },
  { input: 102400, expected: '100 KB', description: '100KB不显示小数' },
  { input: 1048576, expected: '1 MB', description: '1MB' },
  { input: 1073741824, expected: '1 GB', description: '1GB' },
  
  // 异常输入
  { input: '', expected: '0 B', description: '空字符串' },
  { input: null, expected: '0 B', description: 'null值' },
  { input: 'abc', expected: '未知大小', description: '非数字字符串' },
  { input: -100, expected: '未知大小', description: '负数' }
];

// 运行测试
console.log('=== 文件大小格式化功能测试 ===\n');

testCases.forEach((testCase, index) => {
  const result = formatFileSize(testCase.input);
  const isMatch = result === testCase.expected;
  const status = isMatch ? '✓ 通过' : '✗ 失败';
  
  console.log(`测试 ${index + 1}: ${testCase.description}`);
  console.log(`  输入: ${testCase.input}`);
  console.log(`  预期: ${testCase.expected}`);
  console.log(`  实际: ${result}`);
  console.log(`  状态: ${status}`);
  console.log('');
});

// 统计结果
const passedTests = testCases.filter(testCase => 
  formatFileSize(testCase.input) === testCase.expected
).length;

console.log(`=== 测试结果统计 ===`);
console.log(`总测试数: ${testCases.length}`);
console.log(`通过数: ${passedTests}`);
console.log(`失败数: ${testCases.length - passedTests}`);
console.log(`通过率: ${((passedTests / testCases.length) * 100).toFixed(1)}%`);

export { formatFileSize, testCases };
